module.exports =
  server:
    secret_key: 'lightweight'
    name: 'Webhook API Getaway'
    version: '1.0.0'
    port: 8010
    timeout: 60000 * 10

  projects: ['dc', 'jc', 'kd', 'kg', 'mc']

  AWS:
    credentials:
      accessKeyId: ''
      secretAccessKey: ''
      region: 'us-east-1'
  SQS:
    sendgrid_events:
      QueueUrl: 'https://sqs.us-east-1.amazonaws.com/951335658044/DevSendgridEvents-%s'
    roofmail_events:
      QueueUrl: 'https://sqs.us-east-1.amazonaws.com/951335658044/DevRoofmailEvents-%s'
    mailjet_events:
      QueueUrl: 'https://sqs.us-east-1.amazonaws.com/951335658044/DevMailjetEvents-%s'
    startwire_events:
      QueueUrl: 'https://sqs.us-east-1.amazonaws.com/951335658044/DevStartWireEvents'
    max_message_size: 256*1024
