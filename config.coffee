helper = require './app_modules/helpers/config'
fs = require 'fs'
applicationVersion = fs.readFileSync __dirname + '/data/version.txt', 'utf8'
env = process.env.NODE_ENV || 'development';
configPath = __dirname + '/data/config.json';
credentialsPath = __dirname + '/data/credentials.json';

config =
  env: env
  app_dir: __dirname + '/app_modules'
  version: applicationVersion
  server:
    secret_key: 'lightweight'
    name: 'webhooks-api'
    version: '1.0.0'
    port: 7010
    timeout: 60000 * 10

config = helper.mergeFromFile config, configPath, env
config = helper.mergeFromFile config, credentialsPath, env

module.exports = config