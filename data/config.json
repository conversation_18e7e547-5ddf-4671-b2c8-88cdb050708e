{"development": {"projects": ["dc", "jc", "kd", "kg", "mc", "js"], "AWS": {"credentials": {"accessKeyId": "AWS_KEY_ID", "secretAccessKey": "AWS_ACCESS_KEY", "region": "us-east-1"}}, "SQS": {"sendgrid_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/DevSendgridEvents-%s"}, "smtp_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/DevSMTPEvents-%s"}, "smtp_com_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/DevSMTPComEvents-%s"}, "roofmail_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/DevRoofmailEvents-%s"}, "fcm_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/DevFCMEvents-%s"}, "mailjet_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/DevMailjetEvents-%s"}, "startwire_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/DevStartWireEvents"}, "max_message_size": 262144}, "logger": {"transports": {"file": {"level": "debug", "filepath": "data/logs/webhooks-api.log"}, "logstash": {"level": ["warn", "info", "debug", "error"], "host": "logstash.jobsearcher.com", "port": 28887, "type": "udp"}, "console": {"level": ["error"]}}}}, "production": {"projects": ["dc", "jc", "kd", "kg", "mc", "js", "cn"], "AWS": {"credentials": {"accessKeyId": "AWS_KEY_ID", "secretAccessKey": "AWS_ACCESS_KEY", "region": "us-east-1"}}, "SQS": {"sendgrid_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LiveSendgridEvents-%s"}, "smtp_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LiveSMTPEvents-%s"}, "smtp_com_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LiveSMTPComEvents-%s"}, "roofmail_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LiveRoofmailEvents-%s"}, "fcm_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LiveFCMEvents-%s"}, "mailjet_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LiveMailjetEvents-%s"}, "startwire_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/StartWireEvents"}, "general_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LiveEvents-general"}, "max_message_size": 262144}, "gc": {"projectId": "duke-464813", "queueName": "LiveEvents-general"}, "logger": {"transports": {"logstash": {"level": ["info", "debug", "error"], "host": "logstash.jobsearcher.com", "port": 28888, "type": "udp"}, "console": {"level": ["error", "info"]}, "bugsnag": {"level": ["error"], "api_key": "bugsnag_api_key"}}}}, "local": {"projects": ["dc", "jc", "kd", "kg", "mc", "js"], "AWS": {"credentials": {"accessKeyId": "AWS_KEY_ID", "secretAccessKey": "AWS_ACCESS_KEY", "region": "us-east-1"}}, "SQS": {"sendgrid_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LocalSendgridEvents-%s"}, "smtp_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LocalSMTPEvents-%s"}, "smtp_com_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LocalSMTPComEvents-%s"}, "roofmail_events": {"QueueUrl": ""}, "fcm_events": {"QueueUrl": "https://sqs.us-east-1.amazonaws.com/951335658044/LocalFCMEvents-%s"}, "mailjet_events": {"QueueUrl": ""}, "startwire_events": {"QueueUrl": ""}, "max_message_size": 262144}, "logger": {"transports": {"file": {"level": "debug", "filepath": "data/logs/webhooks-api.log"}, "console": {"level": ["debug", "info", "error"]}}}}}