{"name": "duke-webhooks-api-gateway", "version": "1.0.0", "description": "API layer batween sengdrid webhooks and AWS SQS", "scripts": {"test": "./node_modules/.bin/mocha --compilers coffee:coffee-script/register --recursive", "start": "./node_modules/.bin/coffee server.coffee"}, "repository": {"type": "git", "url": "**************:daveigor/duke-webhooks-api.git"}, "author": "", "license": "ISC", "dependencies": {"async": "2.4.1", "aws-sdk": "2.22.0", "chai": "4.1.2", "coffee-script": "1.12.7", "deepmerge": "3.1.0", "duke-logger": "git+ssh://**************:daveigor/duke-logger.git#v1.4.0", "lodash": "4.17.4", "mocha": "5.0.1", "optimist": "0.6.1", "proxyquire": "2.0.0", "qs": "6.4.0", "request": "2.80.0", "restify": "4.3.0", "sinon": "7.2.3", "underscore": "1.8.3", "winston": "2.3.1"}}