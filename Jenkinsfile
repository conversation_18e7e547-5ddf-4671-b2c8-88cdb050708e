def image

def vaultConfiguration = [vaultUrl: 'https://vault.jobsearcher.com',
    vaultCredentialId: 'vault-approle', skipSslVerification: false, engineVersion: 2]

pipeline {
    agent {
        kubernetes {
            yamlFile 'K8sPodAgent.yaml'
            nodeSelector 'processor_type=arm'
        }
    }

    options {
        timestamps()
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', daysToKeepStr: '10'))
        timeout(activity: true, time: 3600, unit: 'SECONDS')
    }

    environment {
        AWS_ACCOUNT = '************'
        REGION = 'us-east-1'
        ECR_REPO = 'dk-webhook-api'
        ECR_URL = "${AWS_ACCOUNT}.dkr.ecr.${REGION}.amazonaws.com"
    }

    parameters {
        choice(name: 'ENVIRONMENT', choices: ['prod', 'dev'], description: 'Deploy to dev or prod?')
        booleanParam(name: 'DE<PERSON>OY', defaultValue: false, description: 'Deploy to ECS?')
    }

    stages{
        stage ('Prepare'){
            steps{
                container('docker') {
                    script {
                        if (env.BRANCH_NAME == 'main') {
                            env.IMAGE = "${BUILD_ID}"
                        }
                        else {
                            env.IMAGE = "${env.BRANCH_NAME}-${BUILD_ID}"
                        }
                    }
                }
            }
        }
        stage ('Build'){
            steps{
                container('docker') {
                    script {
                        withVault(configuration: vaultConfiguration, vaultSecrets: [
                            [path: 'duke-services/github', engineVersion: 2, secretValues: [
                                [envVar: 'ID_RSA', vaultKey: 'github_id_rsa']]],
                            [path: "duke-services/configurations", engineVersion: 2, secretValues: [
                                [envVar: 'CONFIG', vaultKey: "${ECR_REPO}"]]]
                            ]) {

                            writeFile file: 'build/id_rsa', text: env.ID_RSA
                            writeFile file: 'data/credentials.json', text: env.CONFIG

                            image = docker.build("${ECR_REPO}:${IMAGE}")
                        }
                    }
                }
            }
        }
        stage ('Push to ECR'){
            when {
                expression {
                    env.BRANCH_NAME == 'main' || params.DEPLOY == true
                }
            }
            steps{
                container('docker') {
                    script {
                        docker.withRegistry("https://${ECR_URL}", "ecr:${REGION}:duke-jenkins-role") {
                            image.push("${IMAGE}")

                            if (env.BRANCH_NAME == 'main') {
                                image.push('latest')
                            }
                        }
                    }
                }
            }
        }
        stage ('Deploy to ECS'){
            when {
                expression {
                    env.BRANCH_NAME == 'main' || params.DEPLOY == true
                }
            }
            steps{
                container('helm') {
                    script {
                        sh "helm upgrade -i webhook-api k8s/ -f k8s/webhook-api-${ENVIRONMENT}.yaml --namespace duke-${ENVIRONMENT} --atomic --set image.repository=${ECR_URL}/${ECR_REPO} --set image.tag=${IMAGE}"
                    }
                }
            }
        }
    }
}
