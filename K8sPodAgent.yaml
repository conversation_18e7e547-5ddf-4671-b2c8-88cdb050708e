apiVersion: v1
kind: Pod
spec:
  serviceAccountName: jenkins
  containers:
  - name: docker
    image: ************.dkr.ecr.us-east-1.amazonaws.com/infrastructure:docker-aws
    imagePullPolicy: IfNotPresent
    command:
    - cat
    tty: true
    env:
    - name: DOCKER_HOST
      value: tcp://localhost:2375
  - name: dind
    image: "docker:dind"
    imagePullPolicy: IfNotPresent
    command: ["dockerd", "--host", "tcp://127.0.0.1:2375"]
    securityContext:
      privileged: true
  - name: helm
    image: "************.dkr.ecr.us-east-1.amazonaws.com/infrastructure:helm"
    imagePullPolicy: IfNotPresent
    command: ["sleep"]
    args: ["infinity"]