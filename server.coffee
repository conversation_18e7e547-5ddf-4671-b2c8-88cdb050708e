restify = require 'restify'
config = require './config'
logger = require './app_modules/logger'
route = require './app_modules/route'

app = restify.createServer config.server

app.use [
  restify.queryParser()
  restify.bodyParser()
]

route.register app

app.get "/version", (require, restify) ->
  restify.send(config.version)

app.listen config.server.port, ->
  logger.info "#{config.server.name} listening port #{config.server.port}."