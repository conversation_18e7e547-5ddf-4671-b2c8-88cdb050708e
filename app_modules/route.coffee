util = require 'util'
restify = require 'restify'
lodash = require 'lodash'

sqs = require './app_sqs'
config = require '../config'
logger = require './logger'
options = require './options'

pixel = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='

error = (type, message, next) ->
  result =
    body:
      meta:
        code: type
        success: false
        message: message?.errors or message
  if restify[type]
    next new restify[type](result)
  else
#next false
    error_message = "Type #{type} of error not found. Error: #{result.body.message or message}"
    logger.error error_message
    next new restify.errors.InternalServerError error_message


success = (res) ->
  results =
    meta:
      success: true
  res.send results

events_handler = (service, req, res, next) ->
  project = req.context.project
  message = req.body
  sqs_params = lodash.clone config.SQS["#{service}_events"]
  sqs_params.QueueUrl = util.format sqs_params.QueueUrl, project.toLowerCase()

  params =
    message_size: req.headers['content-length']
    sqs_params: sqs_params

  sqs.send_message message, params, (err)->
    return error err, '', next if err
    success res

events_handler_general = (service, req, res, next) ->
  message = req.body
  sqs_params = lodash.clone config.SQS["#{service}_events"]

  params =
    message_size: req.headers['content-length']
    sqs_params: sqs_params

  sqs.send_message message, params, (err)->
    return error err, '', next if err
    success res

events_conversion_handler = (service, req, res, next) ->
  require_params = ['user_id', 'email_id', 'job_id', 'track_id', 'click_id']
  message = lodash.extend req.params, {
    event: 'click'
    timestamp: Math.round(Date.now() / 1000)
    service: service
  }

  lodash.each require_params, (param) ->
    message[param] = message[param] or 0

  sqs_params = lodash.clone config.SQS["#{service}_events"]

  params =
    message_size: 1000
    sqs_params: sqs_params

  sqs.send_message [message], params, (err)->
    return error err, '', next if err
    img = new Buffer(pixel, 'base64');
    res.writeHead(200, {
      'Content-Type': 'image/png',
      'Content-Length': img.length
    });
    res.end(img);

###
  List of routes with all needed params.
  Possible properties:
    url - path
    method - HTTP method
    handler - function that will process data and send response
    version - optional, by default 1.0.0
###
routes = [
  {
    url: '/track_general_events'
    method: 'POST'
    handler: events_handler_general.bind null, 'general'
  }

  {
    url: '/track_sendgrid_events/:project'
    method: 'POST'
    handler: events_handler.bind null, 'sendgrid'
  }

  {
    url: '/track_smtp_events/:project'
    method: 'POST'
    handler: events_handler.bind null, 'smtp'
  }

  {
    url: '/track_smtp_com_events/:project'
    method: 'POST'
    handler: events_handler.bind null, 'smtp_com'
  }

  {
    url: '/track_roofmail_events/:project'
    method: 'POST'
    handler: events_handler.bind null, 'roofmail'
  }

  {
    url: '/track_fcm_events/:project'
    method: 'POST'
    handler: events_handler.bind null, 'fcm'
  }

  {
    url: '/track_mailjet_events/:project'
    method: 'POST'
    handler: events_handler.bind null, 'mailjet'
  }

  {
    url: '/track_convertion_startwire'
    method: 'GET'
    handler: events_conversion_handler.bind null, 'startwire'
  }

  {
    url: '/status_check'
    method: 'GET'
    handler: (req, res, next) ->
      success res
  }
]

register = (app) ->
  for route in routes
    method = route.method.toLowerCase()
    req_params =
      path: route.url
    req_params.version = route.version if route.version

    app[method] req_params, route.handler

module.exports = {register, success, error}
