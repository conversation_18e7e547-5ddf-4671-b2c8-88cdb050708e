AWS = require 'aws-sdk'
config = require '../config'
async = require 'async'
lodash = require 'lodash'


AWS.config.update config.AWS.credentials
sqs = new AWS.SQS()

class AppSQS
  @app: sqs
  ###
  getCoefficient req.size / sqs_limit
  getChunkSize arr.length/coefficient

  ###
  @get_chunk_size: (message, message_size)->
    coefficient = Math.ceil(message_size / config.SQS.max_message_size) + 1
    Math.ceil(message.length / coefficient)


  @get_message_chunks: (message, message_size)=>
    switch typeof message
      when 'object'
        return [message] if message_size <= config.SQS.max_message_size
        chunk_size = @get_chunk_size(message, message_size)
        lodash.chunk message, chunk_size
      else [message]

  @send_message: (message, params, cb)=>
    {message_size, sqs_params} = params

    process_messages = (message, callback)=>
      sqs_params['MessageBody'] = JSON.stringify message
      @app.sendMessage sqs_params, callback

    message_chunks = @get_message_chunks message, message_size
    async.eachSeries message_chunks, process_messages, cb


  @send_message_batch: (message, params, cb)=>



module.exports = AppSQS
