{PubSub} = require '@google-cloud/pubsub'
config = require '../config'
async = require 'async'
lodash = require 'lodash'

# Initialize Google Cloud Pub/Sub client
pubsub = new PubSub(config.gc.credentials)

class AppGC
  @app: pubsub
  
  ###
  Get chunk size based on message size and Google Cloud Pub/Sub limits
  Google Cloud Pub/Sub has a 10MB message size limit
  ###
  @get_chunk_size: (message, message_size)->
    coefficient = Math.ceil(message_size / config.gc.max_message_size) + 1
    Math.ceil(message.length / coefficient)

  @get_message_chunks: (message, message_size)=>
    switch typeof message
      when 'object'
        return [message] if message_size <= config.gc.max_message_size
        chunk_size = @get_chunk_size(message, message_size)
        lodash.chunk message, chunk_size
      else [message]

  @send_message: (message, params, cb)=>
    {message_size, gc_params} = params
    topic_name = gc_params.topicName

    # Get or create topic
    topic = @app.topic(topic_name)

    process_messages = (message, callback)=>
      # Convert message to JSON string
      data = JSON.stringify(message)
      
      # Create message buffer
      dataBuffer = Buffer.from(data)
      
      # Publish message
      topic.publish(dataBuffer)
        .then (messageId) ->
          callback(null, messageId)
        .catch (error) ->
          callback(error)

    message_chunks = @get_message_chunks message, message_size
    async.eachSeries message_chunks, process_messages, cb

module.exports = AppGC
