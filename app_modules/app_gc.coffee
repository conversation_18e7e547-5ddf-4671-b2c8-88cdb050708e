{ PubSub } = require '@google-cloud/pubsub'
config = require '../config'

# Initialize Google Cloud Pub/Sub client
pubsub = new PubSub({projectId: config.gc.projectId})

class AppGC
  @app: pubsub



  @send_message: (message, params, cb)=>
    {gc_params} = params
    topic_name = gc_params.queueName

    # Get or create topic
    topic = @app.topic(topic_name)

    # Convert message to JSON string
    data = JSON.stringify(message)

    # Create message buffer
    dataBuffer = Buffer.from(data)

    # Publish message
    topic.publish(dataBuffer)
      .then (messageId) ->
        cb(null, messageId)
      .catch (error) ->
        cb(error)

module.exports = AppGC
