'use strict'

optimist = require 'optimist'
config = require '../config'

exports.argv = optimist
.usage('Usage: $0 [options]')
.option('ls', default: 1, boolean: false, alias: 'log-slack', describe: 'Specify what kind of messages send to slack: any=7, info=4, warn=2, error=1') #A
.option('pr', boolean: false, alias: 'project', describe: 'Specify projects abbreviation for select')
.check((argv)->
  if /process_queue/.test(argv['$0']) and argv.project not in config.projects
    throw "Incorrect 'pr' value. Possible: #{config.projects}"
)
.argv

if exports.argv.help
  optimist.showHelp()
  process.exit(0)



###
A 7=111 (log Info Warns Errors), 5 = 101 (log Info Errors)), 0 = log nothing, 1 = log errors
###