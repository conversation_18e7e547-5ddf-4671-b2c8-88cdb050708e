image:
  repository: ************.dkr.ecr.us-east-1.amazonaws.com/dk-webhook-api
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: "webhook-api"

serviceAccount:
  create: false
  automount: false
  annotations: {}
  name: ""

podAnnotations: {}
podLabels: {}
podSecurityContext: {}
securityContext: {}

service:
  enabled: true
  type: ClusterIP
  port: 7010

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/group.name: eks-prod-internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.order: '10'
    alb.ingress.kubernetes.io/healthcheck-path: /status_check
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
  hosts:
    - host: webhook-api.jobsearcher.com
      paths:
        - path:
          pathType: ImplementationSpecific

replicaCount: 3

resources:
  requests:
    cpu: 128m
    memory: 256Mi
  limits:
    cpu: 256m
    memory: 512Mi

env:
- name: NODE_ENV
  value: "production"

readinessProbe: 
  httpGet:
    path: /status_check
    port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 10
livenessProbe: 
  httpGet:
    path: /status_check
    port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 10
  
grace_period: 30

lifecycle:
  preStop:
    exec:
      command: ["sleep", "60"]

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 10%
    maxSurge: 20%

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 150
  targetMemoryUtilizationPercentage: 150

nodeSelector:
  subnet_type: private
  architecture: arm

dnsPolicy: Default
volumes: []
volumeMounts: []
tolerations: []
affinity: {}
command: []