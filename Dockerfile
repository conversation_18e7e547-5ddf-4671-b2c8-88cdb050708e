FROM node:8.9.0-alpine

WORKDIR /build_tmp

RUN apk update && apk add openssh git python make gcc g++

RUN mkdir -p /root/.ssh
ADD build/id_rsa /root/.ssh/id_rsa
RUN chmod 700 /root/.ssh/id_rsa

RUN ssh-keyscan github.com  > /root/.ssh/known_hosts

COPY . /build_tmp

RUN npm install

#################

FROM node:8.9.0-alpine

EXPOSE 5858
EXPOSE 7010

WORKDIR /code

COPY --from=0 /build_tmp .

RUN npm test

CMD ["npm", "start"]