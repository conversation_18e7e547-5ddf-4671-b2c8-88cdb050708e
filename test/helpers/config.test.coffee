chai = require 'chai'
proxyquire = require 'proxyquire'

describe 'helpers/config', ->
  describe '#mergeFromFile()', ->
    it 'should return the same object if file not exists', ->
      configMock =
        'fs':
          existsSync: () => false

      utils = proxyquire "../../app_modules/helpers/config", configMock
      json = 
        test: 123

      chai.assert.equal utils.mergeFromFile(json, "test.json", "local"), json

    it 'should return the same object if there isn\'t key for merge', ->
      configMock =
        'fs':
          existsSync: () => true
          readFileSync: () => "{}"

      utils = proxyquire "../../app_modules/helpers/config", configMock
      json = 
        test: 123

      chai.assert.equal utils.mergeFromFile(json, "test.json", "local"), json

    it 'should merge objects if file and key exists', ->
      fileJson = 
        local: 
          'some_key': 2

      configMock =
        'fs':
          existsSync: () => true
          readFileSync: () => JSON.stringify fileJson

      utils = proxyquire "../../app_modules/helpers/config", configMock
      json = 
        test: 123
      expectedJson =
          test: 123
          some_key: 2
      
      chai.assert.deepEqual utils.mergeFromFile(json, "test.json", "local"), expectedJson

    it 'should merge objects if file and key exists and override key', ->
      fileJson = 
        local: 
          'test': 2

      configMock =
        'fs':
          existsSync: () => true
          readFileSync: () => JSON.stringify fileJson

      utils = proxyquire "../../app_modules/helpers/config", configMock
      json = 
        test: 123
      expectedJson =
          test: 2
      
      chai.assert.deepEqual utils.mergeFromFile(json, "test.json", "local"), expectedJson